"""
预览编辑路由
提供预览编辑功能的Flask路由和API接口
"""

import os
import json
import logging
from datetime import datetime
from flask import Flask, request, jsonify, render_template, redirect, url_for
from document.llm_form_converter import LLMFormConverter
from document.web_form_generator import WebFormGenerator
from document.web_form_processor import WebFormProcessor
from utils.preview_storage import get_preview_storage
from document.generator import DocumentGenerator
from document.checkbox.constants import CHECKBOX_CHARS

logger = logging.getLogger(__name__)


def _count_checkboxes_in_document(doc):
    """
    计算Word文档中的checkbox总数

    Args:
        doc: Word文档对象

    Returns:
        int: checkbox的总数
    """
    checkbox_count = 0

    # 计算段落中的checkbox
    for paragraph in doc.paragraphs:
        paragraph_text = paragraph.text
        for char in CHECKBOX_CHARS:
            checkbox_count += paragraph_text.count(char)

    # 计算表格中的checkbox
    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                for paragraph in cell.paragraphs:
                    paragraph_text = paragraph.text
                    for char in CHECKBOX_CHARS:
                        checkbox_count += paragraph_text.count(char)

    return checkbox_count


def _generate_default_party_info():
    """
    生成默认的当事人信息结构，用于直接编辑模板

    Returns:
        dict: 包含默认当事人信息的字典
    """
    return {
        "原告（自然人）列表": [
            {
                "姓名": "",
                "性别": "",
                "民族": "",
                "出生日期": "",
                "证件号码": "",
                "户籍所在地": "",
                "经常居住地": "",
                "工作单位": "",
                "职务": "",
                "联系电话": "",
                "证件类型": ""
            }
        ],
        "原告（法人、非法人组织）列表": [
            {
                "名称": "",
                "住所地": "",
                "注册地": "",
                "法定代表人": "",
                "法定代表人职务": "",
                "法定代表人联系电话": "",
                "统一社会信用代码": "",
                "经营者信息": ""
            }
        ],
        "被告（自然人）列表": [
            {
                "姓名": "",
                "性别": "",
                "民族": "",
                "出生日期": "",
                "证件号码": "",
                "户籍所在地": "",
                "经常居住地": "",
                "工作单位": "",
                "职务": "",
                "联系电话": "",
                "证件类型": ""
            }
        ],
        "被告（法人、非法人组织）列表": [
            {
                "名称": "",
                "住所地": "",
                "注册地": "",
                "法定代表人": "",
                "法定代表人职务": "",
                "法定代表人联系电话": "",
                "统一社会信用代码": "",
                "经营者信息": ""
            }
        ],
        "第三人（自然人）列表": [],
        "第三人（法人、非法人组织）列表": []
    }

def register_preview_edit_routes(app: Flask):
    """注册预览编辑相关的路由"""

    @app.route('/direct-edit-template', methods=['POST'])
    def direct_edit_template():
        """
        直接编辑模板API - 为触摸屏版本提供直接编辑空白模板的功能
        """
        try:
            data = request.get_json()
            template_path = data.get('template_path')
            template_name = data.get('template_name', '')

            if not template_path:
                return jsonify({'error': '缺少模板路径'}), 400

            # 构建完整的模板路径
            full_template_path = os.path.join('static', 'templates', template_path)

            if not os.path.exists(full_template_path):
                return jsonify({'error': f'模板文件不存在: {template_path}'}), 404

            # 生成预览ID用于编辑会话
            import uuid
            preview_id = str(uuid.uuid4())

            # 生成默认的当事人信息
            default_party_info = _generate_default_party_info()

            # 使用中间处理器处理模板，填充当事人插入点
            from document.intermediate_processor import IntermediateDocumentProcessor
            intermediate_processor = IntermediateDocumentProcessor(
                template_path=full_template_path,
                debug=app.config.get('DEBUG', False)
            )

            # 创建包含当事人信息的中间文档
            intermediate_doc, remaining_placeholders = intermediate_processor.create_intermediate_document(default_party_info)

            # 从中间文档重新提取占位符，生成表单数据
            from document.web_form_generator import WebFormGenerator
            form_generator = WebFormGenerator(full_template_path)

            # 使用中间文档生成表单数据
            form_data = form_generator._extract_placeholders_from_doc(intermediate_doc)

            # 将当事人信息也转换为表单字段格式并合并
            from document.llm_form_converter import LLMFormConverter
            converter = LLMFormConverter(debug=app.config.get('DEBUG', False))
            party_form_data = converter._convert_party_info(default_party_info)
            form_data.update(party_form_data)

            # 为直接编辑模式初始化checkbox状态
            # 扫描中间文档中的所有checkbox并设置默认状态为False
            checkbox_count = _count_checkboxes_in_document(intermediate_doc)

            # 为每个checkbox设置默认状态
            for i in range(1, checkbox_count + 1):
                checkbox_key = f"checkbox_{i}"
                form_data[checkbox_key] = False  # 默认未勾选
                logger.debug(f"初始化checkbox状态: {checkbox_key} = False")

            logger.info(f"生成的默认表单数据字段数量: {len(form_data)}")
            logger.info(f"直接编辑模式：初始化了 {checkbox_count} 个checkbox的默认状态")
            if app.config.get('DEBUG', False):
                logger.debug(f"默认表单数据字段: {list(form_data.keys())[:20]}")

            # 将中间文档保存为临时文件，供编辑页面使用
            import tempfile
            import uuid
            temp_doc_id = str(uuid.uuid4())
            temp_doc_path = os.path.join(tempfile.gettempdir(), f"direct_edit_{temp_doc_id}.docx")
            intermediate_doc.save(temp_doc_path)

            # 存储到预览存储中，用于编辑会话
            storage = get_preview_storage()
            current_time = datetime.now()
            preview_data = {
                'template_path': full_template_path,
                'intermediate_doc_path': temp_doc_path,  # 处理过的中间文档路径
                'formatted_text': form_data,  # 包含默认当事人信息的表单数据
                'party_info': default_party_info,  # 原始当事人信息结构
                'created_at': current_time.isoformat(),
                'expires_at': (current_time.replace(hour=23, minute=59, second=59, microsecond=999999)).isoformat(),
                'is_direct_edit': True,  # 标记为直接编辑模式
                'template_name': template_name
            }

            # 使用save_preview_data方法，但需要适配参数
            # 由于save_preview_data需要formatted_text, template_path, file_id参数
            # 我们需要直接调用内部方法或修改存储逻辑

            # 临时解决方案：直接调用内部存储方法
            if storage.storage_type == 'file':
                storage._save_to_file(preview_id, preview_data)
            else:
                storage._save_to_memory(preview_id, preview_data)

            # 返回编辑页面URL
            edit_url = f'/edit-document/{preview_id}'

            logger.info(f"直接编辑模板准备成功: {template_path} -> {edit_url}")

            return jsonify({
                'success': True,
                'edit_url': edit_url,
                'preview_id': preview_id,
                'template_name': template_name
            })

        except Exception as e:
            logger.error(f"直接编辑模板时出错: {str(e)}")
            return jsonify({'error': f'准备编辑失败: {str(e)}'}), 500

    @app.route('/edit-document/<preview_id>')
    def show_wysiwyg_edit(preview_id):
        """
        显示所见即所得编辑页面

        Args:
            preview_id (str): 预览数据ID
        """
        try:
            # 获取预览数据
            storage = get_preview_storage()
            preview_data = storage.get_preview_data(preview_id)
            
            if not preview_data:
                return "预览数据不存在或已过期", 404
            
            # 获取模板路径和完整的预览数据
            template_path = preview_data['template_path']
            complete_preview_data = preview_data['formatted_text']  # 这里实际包含完整的预览数据
            is_direct_edit = preview_data.get('is_direct_edit', False)  # 检查是否为直接编辑模式

            if not os.path.exists(template_path):
                return f"模板文件不存在: {template_path}", 404

            # 处理直接编辑模式（预填充当事人信息的模板）
            if is_direct_edit:
                print("📝 直接编辑模式：使用预填充的当事人信息")
                form_data = complete_preview_data  # 使用预填充的表单数据

                # 使用中间文档路径替代原始模板路径
                intermediate_doc_path = preview_data.get('intermediate_doc_path')
                if intermediate_doc_path and os.path.exists(intermediate_doc_path):
                    template_path = intermediate_doc_path
                    print(f"📝 直接编辑模式使用中间文档: {intermediate_doc_path}")
                else:
                    print("⚠️ 中间文档不存在，使用原始模板")

                print(f"📝 直接编辑模式表单数据字段数量: {len(form_data)}")
                if app.config.get('DEBUG', False):
                    print(f"📝 直接编辑模式表单数据字段: {list(form_data.keys())[:10]}")
            else:
                print(f"编辑页面获取到的预览数据字段: {list(complete_preview_data.keys())}")

                # 检查是否包含完整的文档内容
                if '_complete_document_text' in complete_preview_data:
                    print("✅ 发现完整文档内容，使用完整预览数据")
                    form_data = complete_preview_data  # 直接使用完整数据
                else:
                    print("⚠️  未发现完整文档内容，使用LLM转换器")
                    # 转换LLM数据为表单数据（向后兼容）
                    converter = LLMFormConverter(debug=app.config.get('DEBUG', False))
                    form_data = converter.convert_to_form_data(complete_preview_data, template_path)
                    print(f"LLM转换器生成的表单数据字段数量: {len(form_data)}")
                    print(f"表单数据示例字段: {list(form_data.keys())[:10]}")

                    # 检查关键数据是否存在
                    key_fields = ['原告自然人1_姓名', '被告自然人1_姓名', '离婚理由', '诉讼的事实和理由']
                    found_fields = [f for f in key_fields if f in form_data and form_data[f]]
                    print(f"找到的关键字段: {found_fields}")

                    if found_fields:
                        print("✅ 表单数据包含有效内容")
                    else:
                        print("❌ 表单数据可能为空或无效")

            # 生成所见即所得的编辑表单HTML
            form_generator = WebFormGenerator(template_path)
            form_html = form_generator.generate_wysiwyg_edit_form(form_data)

            # 渲染所见即所得编辑页面
            return render_template('wysiwyg_edit.html',
                                 preview_id=preview_id,
                                 form_html=form_html,
                                 template_name=os.path.basename(template_path),
                                 form_data=json.dumps(form_data, ensure_ascii=False))
            
        except Exception as e:
            logger.error(f"显示预览编辑页面时出错: {str(e)}")
            return f"加载预览编辑页面失败: {str(e)}", 500
    
    @app.route('/api/edit-data/<preview_id>')
    def get_edit_data_api(preview_id):
        """
        获取编辑数据API

        Args:
            preview_id (str): 预览数据ID
        """
        try:
            storage = get_preview_storage()
            preview_data = storage.get_preview_data(preview_id)
            
            if not preview_data:
                return jsonify({'error': '预览数据不存在或已过期'}), 404
            
            # 获取完整的预览数据
            complete_preview_data = preview_data['formatted_text']  # 这里实际包含完整的预览数据

            # 检查是否包含完整的文档内容
            if '_complete_document_text' in complete_preview_data:
                print("✅ API发现完整文档内容，使用完整预览数据")
                form_data = complete_preview_data  # 直接使用完整数据
            else:
                print("⚠️  API未发现完整文档内容，使用LLM转换器")
                # 转换LLM数据为表单数据（向后兼容）
                converter = LLMFormConverter(debug=app.config.get('DEBUG', False))
                form_data = converter.convert_to_form_data(
                    complete_preview_data,
                    preview_data['template_path']
                )
            
            return jsonify({
                'success': True,
                'preview_id': preview_id,
                'template_path': preview_data['template_path'],
                'template_name': os.path.basename(preview_data['template_path']),
                'form_data': form_data,
                'created_at': preview_data['created_at']
            })
            
        except Exception as e:
            logger.error(f"获取预览数据时出错: {str(e)}")
            return jsonify({'error': f'获取预览数据失败: {str(e)}'}), 500
    
    @app.route('/api/generate-from-preview', methods=['POST'])
    def generate_from_preview():
        """
        从预览编辑生成文档API
        """
        try:
            data = request.get_json()
            preview_id = data.get('preview_id')
            form_data = data.get('form_data', {})
            
            if not preview_id:
                return jsonify({'error': '缺少预览ID'}), 400
            
            # 获取原始预览数据
            storage = get_preview_storage()
            preview_data = storage.get_preview_data(preview_id)
            
            if not preview_data:
                return jsonify({'error': '预览数据不存在或已过期'}), 404
            
            template_path = preview_data['template_path']
            complete_preview_data = preview_data['formatted_text']  # 这里包含完整的预览数据

            # 检查是否为直接编辑模式
            is_direct_edit = preview_data.get('is_direct_edit', False)

            print(f"📍 [编辑页面生成] 原始模板路径: {template_path}")
            print(f"📍 [编辑页面生成] 是否直接编辑模式: {is_direct_edit}")

            # 转换表单数据为LLM格式
            converter = LLMFormConverter(debug=app.config.get('DEBUG', False))
            llm_data = converter.convert_form_data_to_llm_format(form_data)

            # 使用现有的DocumentGenerator生成文档
            download_dir = os.path.join(app.static_folder, 'downloads')
            os.makedirs(download_dir, exist_ok=True)

            if is_direct_edit:
                # 直接编辑模式：使用原始模板，重新处理当事人信息
                actual_template_path = template_path
                doc_generator_config = {
                    'process_checkboxes': False,  # 跳过CHECKBOX处理
                    'checkbox_llm_api_url': app.config.get('CHECKBOX_LLM_API_URL'),
                    'checkbox_llm_api_key': app.config.get('CHECKBOX_LLM_API_KEY'),
                    'checkbox_llm_model': app.config.get('CHECKBOX_LLM_MODEL'),
                    'process_checkbox_batch': False,  # 跳过批量处理
                    'combine': app.config.get('COMBINE', False),
                    'skip_checkbox_processing': True,  # 跳过CHECKBOX处理
                    'skip_party_processing': False  # 重新处理当事人信息（用户可能修改了）
                }
                print("📍 [编辑页面生成] 直接编辑模式：使用原始模板，重新处理当事人信息")
            else:
                # 普通编辑模式：尝试使用中间文档
                intermediate_doc_path = complete_preview_data.get('_intermediate_doc_path')
                if intermediate_doc_path and os.path.exists(intermediate_doc_path):
                    actual_template_path = intermediate_doc_path
                    print(f"📍 [编辑页面生成] 使用中间文档: {actual_template_path}")
                else:
                    actual_template_path = template_path
                    print(f"📍 [编辑页面生成] 中间文档不存在，使用原始模板: {actual_template_path}")

                doc_generator_config = {
                    'process_checkboxes': False,  # 从编辑页面生成时跳过CHECKBOX处理
                    'checkbox_llm_api_url': app.config.get('CHECKBOX_LLM_API_URL'),
                    'checkbox_llm_api_key': app.config.get('CHECKBOX_LLM_API_KEY'),
                    'checkbox_llm_model': app.config.get('CHECKBOX_LLM_MODEL'),
                    'process_checkbox_batch': False,  # 跳过批量处理
                    'combine': app.config.get('COMBINE', False),
                    'skip_checkbox_processing': True  # 明确标记跳过CHECKBOX处理
                }
                print("📍 [编辑页面生成] 普通编辑模式：配置为跳过CHECKBOX处理")

            if not os.path.exists(actual_template_path):
                return jsonify({'error': '模板文件不存在'}), 404

            # 创建文档生成器 - 使用中间文档作为模板
            doc_generator = DocumentGenerator(
                template_path=actual_template_path,
                llm_api_url=app.config.get('LLM_API_URL'),
                llm_api_key=app.config.get('LLM_API_KEY'),
                config=doc_generator_config
            )

            # 生成文档
            output_path = doc_generator.generate_document(llm_data, download_dir)
            
            if output_path:
                # 生成下载URL
                filename = os.path.basename(output_path)
                download_url = f"/download/{filename}"
                
                # 清理预览数据
                storage.clear_preview_data(preview_id)
                
                return jsonify({
                    'success': True,
                    'download_url': download_url,
                    'filename': filename,
                    'message': '文档生成成功'
                })
            else:
                return jsonify({'error': '文档生成失败'}), 500
                
        except Exception as e:
            logger.error(f"从预览生成文档时出错: {str(e)}")
            return jsonify({'error': f'生成文档失败: {str(e)}'}), 500
    
    @app.route('/api/skip-preview/<preview_id>', methods=['POST'])
    def skip_preview_edit(preview_id):
        """
        跳过预览编辑，直接生成文档API
        
        Args:
            preview_id (str): 预览数据ID
        """
        try:
            # 获取预览数据
            storage = get_preview_storage()
            preview_data = storage.get_preview_data(preview_id)
            
            if not preview_data:
                return jsonify({'error': '预览数据不存在或已过期'}), 404
            
            template_path = preview_data['template_path']
            formatted_text = preview_data['formatted_text']
            
            if not os.path.exists(template_path):
                return jsonify({'error': '模板文件不存在'}), 404
            
            # 直接使用原始LLM数据生成文档
            download_dir = os.path.join(app.static_folder, 'downloads')
            os.makedirs(download_dir, exist_ok=True)
            
            # 创建文档生成器配置
            doc_generator_config = {
                'process_checkboxes': app.config.get('PROCESS_CHECKBOXES', True),
                'checkbox_llm_api_url': app.config.get('CHECKBOX_LLM_API_URL'),
                'checkbox_llm_api_key': app.config.get('CHECKBOX_LLM_API_KEY'),
                'checkbox_llm_model': app.config.get('CHECKBOX_LLM_MODEL'),
                'process_checkbox_batch': app.config.get('PROCESS_CHECKBOX_BATCH', False),
                'combine': app.config.get('COMBINE', False)
            }
            
            # 创建文档生成器
            doc_generator = DocumentGenerator(
                template_path=template_path,
                llm_api_url=app.config.get('LLM_API_URL'),
                llm_api_key=app.config.get('LLM_API_KEY'),
                config=doc_generator_config
            )
            
            # 生成文档
            output_path = doc_generator.generate_document(formatted_text, download_dir)
            
            if output_path:
                # 生成下载URL
                filename = os.path.basename(output_path)
                download_url = f"/download/{filename}"
                
                # 清理预览数据
                storage.clear_preview_data(preview_id)
                
                return jsonify({
                    'success': True,
                    'download_url': download_url,
                    'filename': filename,
                    'message': '文档生成成功'
                })
            else:
                return jsonify({'error': '文档生成失败'}), 500
                
        except Exception as e:
            logger.error(f"跳过预览生成文档时出错: {str(e)}")
            return jsonify({'error': f'生成文档失败: {str(e)}'}), 500
    
    @app.route('/api/preview-storage-stats')
    def get_preview_storage_stats():
        """获取预览存储统计信息API"""
        try:
            storage = get_preview_storage()
            stats = storage.get_storage_stats()
            
            return jsonify({
                'success': True,
                'stats': stats
            })
            
        except Exception as e:
            logger.error(f"获取存储统计信息时出错: {str(e)}")
            return jsonify({'error': f'获取统计信息失败: {str(e)}'}), 500
    
    @app.route('/api/cleanup-preview-data', methods=['POST'])
    def cleanup_preview_data():
        """清理过期预览数据API"""
        try:
            storage = get_preview_storage()
            cleaned_count = storage.cleanup_expired_data()
            
            return jsonify({
                'success': True,
                'cleaned_count': cleaned_count,
                'message': f'清理了 {cleaned_count} 个过期数据'
            })
            
        except Exception as e:
            logger.error(f"清理预览数据时出错: {str(e)}")
            return jsonify({'error': f'清理失败: {str(e)}'}), 500
