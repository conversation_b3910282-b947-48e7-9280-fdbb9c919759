#!/usr/bin/env python3
"""
测试checkbox集成功能的脚本
模拟完整的直接编辑流程
"""

import os
import sys
import tempfile
from docx import Document

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from document.generator import DocumentGenerator
from document.checkbox.constants import CHECKBOX_CHARS


def create_test_template():
    """创建一个测试模板文档"""
    doc = Document()
    
    # 添加标题
    doc.add_heading('测试起诉状模板', 0)
    
    # 添加当事人信息部分
    doc.add_paragraph("原告：{{原告姓名}}，性别：□男 □女")
    doc.add_paragraph("被告：{{被告姓名}}，性别：□男 □女")
    
    # 添加诉讼请求
    doc.add_paragraph("诉讼请求：")
    doc.add_paragraph("1. 请求法院判决：□支持 □驳回")
    doc.add_paragraph("2. 是否申请财产保全：□是 □否")
    
    return doc


def test_checkbox_simple_replacement():
    """测试简单checkbox替换功能"""
    print("=== 测试简单checkbox替换功能 ===")
    
    # 创建测试模板
    template_doc = create_test_template()
    
    # 保存到临时文件
    with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
        template_path = temp_file.name
        template_doc.save(template_path)
    
    try:
        # 创建文档生成器，配置为使用简单替换
        config = {
            'skip_checkbox_processing': True,  # 使用简单替换
            'process_checkboxes': False
        }
        
        generator = DocumentGenerator(template_path=template_path, config=config)
        
        # 准备测试数据
        test_data = {
            '原告姓名': '张三',
            '被告姓名': '李四',
            'checkbox_1': True,   # 原告性别：男
            'checkbox_2': False,  # 原告性别：女
            'checkbox_3': False,  # 被告性别：男
            'checkbox_4': True,   # 被告性别：女
            'checkbox_5': True,   # 支持
            'checkbox_6': False,  # 驳回
            'checkbox_7': False,  # 申请财产保全：是
            'checkbox_8': True,   # 申请财产保全：否
        }
        
        print(f"测试数据: {test_data}")
        
        # 生成文档
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = generator.generate_document(test_data, temp_dir)
            
            if output_path and os.path.exists(output_path):
                # 读取生成的文档并检查checkbox状态
                generated_doc = Document(output_path)
                
                # 收集所有段落文本
                all_text = []
                for paragraph in generated_doc.paragraphs:
                    all_text.append(paragraph.text)
                
                full_text = '\n'.join(all_text)
                print(f"生成的文档内容:\n{full_text}")
                
                # 检查checkbox状态
                expected_results = [
                    ('张三', '原告姓名应该被替换'),
                    ('李四', '被告姓名应该被替换'),
                    ('☑男', '原告性别应该选择男'),
                    ('□女', '原告性别不应该选择女'),
                    ('□男', '被告性别不应该选择男'),
                    ('☑女', '被告性别应该选择女'),
                    ('☑支持', '应该选择支持'),
                    ('□驳回', '不应该选择驳回'),
                    ('□是', '不应该申请财产保全'),
                    ('☑否', '应该选择否')
                ]
                
                passed = 0
                total = len(expected_results)
                
                for expected, description in expected_results:
                    if expected in full_text:
                        print(f"✅ {description}")
                        passed += 1
                    else:
                        print(f"❌ {description} - 未找到: {expected}")
                
                print(f"\n测试结果: {passed}/{total}")
                
                if passed == total:
                    print("🎉 简单checkbox替换测试通过！")
                    return True
                else:
                    print("❌ 简单checkbox替换测试失败")
                    return False
            else:
                print("❌ 文档生成失败")
                return False
                
    finally:
        # 清理临时文件
        if os.path.exists(template_path):
            os.unlink(template_path)


def test_checkbox_counting_consistency():
    """测试checkbox计数一致性"""
    print("\n=== 测试checkbox计数一致性 ===")
    
    # 创建测试模板
    template_doc = create_test_template()
    
    # 计算原始模板中的checkbox数量
    original_count = 0
    for paragraph in template_doc.paragraphs:
        for char in CHECKBOX_CHARS:
            original_count += paragraph.text.count(char)
    
    print(f"原始模板中的checkbox数量: {original_count}")
    
    # 模拟中间文档处理（添加当事人信息）
    # 这里简化处理，实际中间文档可能会有更多变化
    intermediate_doc = create_test_template()
    
    # 计算中间文档中的checkbox数量
    intermediate_count = 0
    for paragraph in intermediate_doc.paragraphs:
        for char in CHECKBOX_CHARS:
            intermediate_count += paragraph.text.count(char)
    
    print(f"中间文档中的checkbox数量: {intermediate_count}")
    
    if original_count == intermediate_count:
        print("✅ checkbox计数一致性测试通过")
        return True
    else:
        print("❌ checkbox计数一致性测试失败")
        return False


def main():
    """主测试函数"""
    print("开始测试checkbox集成功能...\n")
    
    tests = [
        test_checkbox_counting_consistency,
        test_checkbox_simple_replacement
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n=== 最终测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有集成测试通过！checkbox功能应该正常工作")
        return True
    else:
        print("❌ 部分集成测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    main()
