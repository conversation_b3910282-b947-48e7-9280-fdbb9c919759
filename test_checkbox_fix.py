#!/usr/bin/env python3
"""
测试checkbox修复的脚本
验证直接编辑模式下checkbox状态能否正确保存
"""

import os
import sys
import tempfile
from docx import Document

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from document.checkbox.constants import CHECKBOX_CHARS


def _count_checkboxes_in_document(doc):
    """
    计算Word文档中的checkbox总数
    
    Args:
        doc: Word文档对象
        
    Returns:
        int: checkbox的总数
    """
    checkbox_count = 0
    
    # 计算段落中的checkbox
    for paragraph in doc.paragraphs:
        paragraph_text = paragraph.text
        for char in CHECKBOX_CHARS:
            checkbox_count += paragraph_text.count(char)
    
    # 计算表格中的checkbox
    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                for paragraph in cell.paragraphs:
                    paragraph_text = paragraph.text
                    for char in CHECKBOX_CHARS:
                        checkbox_count += paragraph_text.count(char)
    
    return checkbox_count


def create_test_document():
    """创建一个包含checkbox的测试文档"""
    doc = Document()
    
    # 添加一些包含checkbox的段落
    doc.add_paragraph("这是一个测试文档")
    doc.add_paragraph("选择性别：□男 □女")
    doc.add_paragraph("是否同意：□是 □否")
    
    # 添加一个包含checkbox的表格
    table = doc.add_table(rows=2, cols=2)
    table.cell(0, 0).text = "选项"
    table.cell(0, 1).text = "状态"
    table.cell(1, 0).text = "选项A □"
    table.cell(1, 1).text = "选项B □"
    
    return doc


def test_checkbox_counting():
    """测试checkbox计数功能"""
    print("=== 测试checkbox计数功能 ===")
    
    # 创建测试文档
    doc = create_test_document()
    
    # 计算checkbox数量
    checkbox_count = _count_checkboxes_in_document(doc)
    
    print(f"文档中的checkbox总数: {checkbox_count}")
    
    # 验证结果
    expected_count = 6  # 2个性别选择 + 2个是否选择 + 2个表格选项
    if checkbox_count == expected_count:
        print("✅ checkbox计数测试通过")
        return True
    else:
        print(f"❌ checkbox计数测试失败，期望{expected_count}个，实际{checkbox_count}个")
        return False


def test_form_data_initialization():
    """测试表单数据初始化"""
    print("\n=== 测试表单数据初始化 ===")
    
    # 创建测试文档
    doc = create_test_document()
    
    # 模拟直接编辑模式的初始化过程
    form_data = {}
    checkbox_count = _count_checkboxes_in_document(doc)
    
    # 为每个checkbox设置默认状态
    for i in range(1, checkbox_count + 1):
        checkbox_key = f"checkbox_{i}"
        form_data[checkbox_key] = False  # 默认未勾选
    
    print(f"初始化的checkbox字段: {list(form_data.keys())}")
    print(f"所有checkbox默认状态: {list(form_data.values())}")
    
    # 验证结果
    expected_keys = [f"checkbox_{i}" for i in range(1, checkbox_count + 1)]
    if list(form_data.keys()) == expected_keys and all(v == False for v in form_data.values()):
        print("✅ 表单数据初始化测试通过")
        return True
    else:
        print("❌ 表单数据初始化测试失败")
        return False


def test_checkbox_state_modification():
    """测试checkbox状态修改"""
    print("\n=== 测试checkbox状态修改 ===")
    
    # 创建测试文档
    doc = create_test_document()
    
    # 初始化表单数据
    form_data = {}
    checkbox_count = _count_checkboxes_in_document(doc)
    
    for i in range(1, checkbox_count + 1):
        checkbox_key = f"checkbox_{i}"
        form_data[checkbox_key] = False
    
    # 模拟用户勾选一些checkbox
    form_data["checkbox_1"] = True  # 勾选第一个
    form_data["checkbox_3"] = True  # 勾选第三个
    
    print(f"修改后的checkbox状态: {form_data}")
    
    # 验证结果
    if form_data["checkbox_1"] == True and form_data["checkbox_3"] == True and form_data["checkbox_2"] == False:
        print("✅ checkbox状态修改测试通过")
        return True
    else:
        print("❌ checkbox状态修改测试失败")
        return False


def main():
    """主测试函数"""
    print("开始测试checkbox修复功能...\n")
    
    tests = [
        test_checkbox_counting,
        test_form_data_initialization,
        test_checkbox_state_modification
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！checkbox修复功能正常工作")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    main()
