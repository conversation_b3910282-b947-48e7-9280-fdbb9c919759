#!/usr/bin/env python3
"""
调试checkbox完整流程的脚本
模拟从直接编辑到文档生成的完整过程
"""

import os
import sys
import tempfile
import json
from docx import Document

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from document.generator import DocumentGenerator
from document.web_form_generator import WebFormGenerator
from document.llm_form_converter import LLMFormConverter
from document.party_info_processor import PartyInfoProcessor
from document.intermediate_processor import IntermediateDocumentProcessor
from document.checkbox.constants import CHECKBOX_CHARS


def _count_checkboxes_in_document(doc):
    """计算Word文档中的checkbox总数"""
    checkbox_count = 0
    
    # 计算段落中的checkbox
    for paragraph in doc.paragraphs:
        paragraph_text = paragraph.text
        for char in CHECKBOX_CHARS:
            checkbox_count += paragraph_text.count(char)
    
    # 计算表格中的checkbox
    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                for paragraph in cell.paragraphs:
                    paragraph_text = paragraph.text
                    for char in CHECKBOX_CHARS:
                        checkbox_count += paragraph_text.count(char)
    
    return checkbox_count


def create_realistic_template():
    """创建一个更真实的模板文档"""
    doc = Document()
    
    # 添加标题
    doc.add_heading('民事起诉状', 0)
    
    # 添加当事人信息
    doc.add_paragraph("原告：{{原告姓名}}，性别：□男 □女，年龄：{{原告年龄}}岁")
    doc.add_paragraph("住址：{{原告住址}}")
    doc.add_paragraph("联系电话：{{原告电话}}")
    
    doc.add_paragraph("被告：{{被告姓名}}，性别：□男 □女，年龄：{{被告年龄}}岁")
    doc.add_paragraph("住址：{{被告住址}}")
    doc.add_paragraph("联系电话：{{被告电话}}")
    
    # 添加诉讼请求
    doc.add_paragraph("诉讼请求：")
    doc.add_paragraph("1. 请求法院判决被告支付欠款：□同意 □不同意")
    doc.add_paragraph("2. 请求法院判决被告承担诉讼费用：□同意 □不同意")
    doc.add_paragraph("3. 是否申请财产保全：□是 □否")
    
    return doc


def simulate_direct_edit_flow():
    """模拟完整的直接编辑流程"""
    print("=== 模拟完整的直接编辑流程 ===")
    
    # 步骤1: 创建原始模板
    print("\n步骤1: 创建原始模板")
    template_doc = create_realistic_template()
    
    with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
        template_path = temp_file.name
        template_doc.save(template_path)
    
    print(f"原始模板路径: {template_path}")
    original_checkbox_count = _count_checkboxes_in_document(template_doc)
    print(f"原始模板checkbox数量: {original_checkbox_count}")
    
    try:
        # 步骤2: 生成默认当事人信息（模拟direct_edit_template路由）
        print("\n步骤2: 生成默认当事人信息")
        party_processor = PartyInfoProcessor()
        default_party_info = party_processor.generate_default_party_info()
        
        # 步骤3: 创建中间文档
        print("\n步骤3: 创建中间文档")
        intermediate_processor = IntermediateDocumentProcessor(
            template_path=template_path,
            llm_api_url=None,
            llm_api_key=None
        )
        intermediate_doc, remaining_placeholders = intermediate_processor.create_intermediate_document(default_party_info)
        
        # 保存中间文档
        with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
            intermediate_doc_path = temp_file.name
            intermediate_doc.save(intermediate_doc_path)
        
        print(f"中间文档路径: {intermediate_doc_path}")
        intermediate_checkbox_count = _count_checkboxes_in_document(intermediate_doc)
        print(f"中间文档checkbox数量: {intermediate_checkbox_count}")
        
        # 步骤4: 从中间文档生成表单数据
        print("\n步骤4: 从中间文档生成表单数据")
        form_generator = WebFormGenerator(template_path)
        form_data = form_generator._extract_placeholders_from_doc(intermediate_doc)
        
        # 步骤5: 初始化checkbox状态
        print("\n步骤5: 初始化checkbox状态")
        checkbox_count = _count_checkboxes_in_document(intermediate_doc)
        
        for i in range(1, checkbox_count + 1):
            checkbox_key = f"checkbox_{i}"
            form_data[checkbox_key] = False  # 默认未勾选
        
        print(f"初始化的checkbox字段: {[k for k in form_data.keys() if k.startswith('checkbox_')]}")
        
        # 步骤6: 模拟用户在编辑页面勾选checkbox
        print("\n步骤6: 模拟用户勾选checkbox")
        # 模拟用户勾选一些checkbox
        user_selections = {
            'checkbox_1': True,   # 原告性别：男
            'checkbox_2': False,  # 原告性别：女
            'checkbox_3': False,  # 被告性别：男
            'checkbox_4': True,   # 被告性别：女
            'checkbox_5': True,   # 同意支付欠款
            'checkbox_6': False,  # 不同意支付欠款
            'checkbox_7': True,   # 同意承担诉讼费
            'checkbox_8': False,  # 不同意承担诉讼费
            'checkbox_9': False,  # 申请财产保全：是
            'checkbox_10': True,  # 申请财产保全：否
        }
        
        # 更新表单数据
        for key, value in user_selections.items():
            if key in form_data:
                form_data[key] = value
        
        print(f"用户选择: {user_selections}")
        
        # 步骤7: 转换表单数据为LLM格式
        print("\n步骤7: 转换表单数据为LLM格式")
        converter = LLMFormConverter(debug=True)
        llm_data = converter.convert_form_data_to_llm_format(form_data)
        
        print(f"转换后的LLM数据中的checkbox字段: {[k for k in llm_data.keys() if k.startswith('checkbox_')]}")
        
        # 步骤8: 生成最终文档
        print("\n步骤8: 生成最终文档")
        config = {
            'skip_checkbox_processing': True,  # 使用简单替换
            'process_checkboxes': False,
            'skip_party_processing': True
        }
        
        generator = DocumentGenerator(template_path=intermediate_doc_path, config=config)
        
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = generator.generate_document(llm_data, temp_dir)
            
            if output_path and os.path.exists(output_path):
                print(f"文档生成成功: {output_path}")
                
                # 步骤9: 验证生成的文档
                print("\n步骤9: 验证生成的文档")
                generated_doc = Document(output_path)
                
                # 收集所有文本
                all_text = []
                for paragraph in generated_doc.paragraphs:
                    if paragraph.text.strip():
                        all_text.append(paragraph.text)
                
                full_text = '\n'.join(all_text)
                print("生成的文档内容:")
                print("=" * 50)
                print(full_text)
                print("=" * 50)
                
                # 检查checkbox状态
                checkbox_results = []
                expected_checkboxes = [
                    ('☑男', '原告应该选择男性'),
                    ('□女', '原告不应该选择女性'),
                    ('□男', '被告不应该选择男性'),
                    ('☑女', '被告应该选择女性'),
                    ('☑同意', '应该同意支付欠款'),
                    ('☑同意', '应该同意承担诉讼费'),
                    ('□是', '不应该申请财产保全'),
                    ('☑否', '应该选择不申请财产保全')
                ]
                
                for expected, description in expected_checkboxes:
                    if expected in full_text:
                        checkbox_results.append(f"✅ {description}")
                    else:
                        checkbox_results.append(f"❌ {description} - 未找到: {expected}")
                
                print("\nCheckbox验证结果:")
                for result in checkbox_results:
                    print(result)
                
                success_count = len([r for r in checkbox_results if r.startswith('✅')])
                total_count = len(checkbox_results)
                
                print(f"\n验证结果: {success_count}/{total_count}")
                
                if success_count == total_count:
                    print("🎉 完整流程测试成功！")
                    return True
                else:
                    print("❌ 完整流程测试失败")
                    return False
            else:
                print("❌ 文档生成失败")
                return False
                
    finally:
        # 清理临时文件
        for path in [template_path, intermediate_doc_path]:
            if os.path.exists(path):
                os.unlink(path)


def main():
    """主函数"""
    print("开始调试checkbox完整流程...\n")
    
    if simulate_direct_edit_flow():
        print("\n🎉 所有测试通过！checkbox功能应该正常工作")
        return True
    else:
        print("\n❌ 测试失败，需要进一步调试")
        return False


if __name__ == "__main__":
    main()
